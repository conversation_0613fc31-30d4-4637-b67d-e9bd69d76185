package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins for testing
	},
}

type GameServer struct {
	games map[string]*DotsAndBoxesInstance
}

func NewGameServer() *GameServer {
	return &GameServer{
		games: make(map[string]*DotsAndBoxesInstance),
	}
}

func (gs *GameServer) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}
	defer conn.Close()

	// For testing, create a simple room
	roomID := "test-room"
	playerID := fmt.Sprintf("player-%d", len(gs.games))
	
	// Create game instance if it doesn't exist
	if _, exists := gs.games[roomID]; !exists {
		game := NewGame()
		config := make(map[string]interface{})
		gs.games[roomID] = game.NewInstance(config, roomID).(*DotsAndBoxesInstance)
	}

	gameInstance := gs.games[roomID]
	
	// Add player to game
	playerData := map[string]interface{}{
		"username": fmt.Sprintf("Player %d", len(gameInstance.playerInfo)+1),
	}
	
	err = gameInstance.HandlePlayerJoin(nil, playerID, "session-"+playerID, playerData)
	if err != nil {
		log.Printf("Error adding player: %v", err)
		return
	}

	// Send initial game state
	gameState := gameInstance.GetGameState(playerID)
	response := map[string]interface{}{
		"type":  "personal_state",
		"state": gameState,
	}
	conn.WriteJSON(response)

	// Handle messages
	for {
		var msg map[string]interface{}
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("WebSocket read error: %v", err)
			break
		}

		switch msg["type"] {
		case "game_action":
			action := msg["action"]
			err := gameInstance.HandlePlayerAction(nil, playerID, action)
			if err != nil {
				errorResponse := map[string]interface{}{
					"type":    "error",
					"message": err.Error(),
				}
				conn.WriteJSON(errorResponse)
			} else {
				// Broadcast updated state to all players
				publicState := gameInstance.GetPublicState()
				updateResponse := map[string]interface{}{
					"type":  "game_update",
					"state": publicState,
				}
				conn.WriteJSON(updateResponse)
				
				// Check if game is over
				if gameInstance.IsGameOver() {
					gameOverResponse := map[string]interface{}{
						"type":   "game_over",
						"winner": gameInstance.GetWinner(),
						"state":  publicState,
					}
					conn.WriteJSON(gameOverResponse)
				}
			}
		case "ping":
			pongResponse := map[string]interface{}{
				"type": "pong",
			}
			conn.WriteJSON(pongResponse)
		}
	}

	// Clean up when player disconnects
	gameInstance.HandlePlayerLeave(nil, playerID)
}

func (gs *GameServer) handleRoomService(w http.ResponseWriter, r *http.Request) {
	// Return WebSocket URL for the client
	wsURL := fmt.Sprintf("ws://%s/ws", r.Host)
	response := map[string]string{
		"wsURL": wsURL,
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (gs *GameServer) handleReturnURL(w http.ResponseWriter, r *http.Request) {
	// Return a simple return URL for testing
	response := map[string]string{
		"returnUrl": "/",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func serveStaticFiles(w http.ResponseWriter, r *http.Request) {
	// Serve the HTML file
	if r.URL.Path == "/" || r.URL.Path == "/game-play/" {
		http.ServeFile(w, r, "index.html")
		return
	}
	
	// Serve other static files
	filePath := strings.TrimPrefix(r.URL.Path, "/")
	if filePath == "" {
		filePath = "index.html"
	}
	
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.NotFound(w, r)
		return
	}
	
	http.ServeFile(w, r, filePath)
}

func main() {
	gameServer := NewGameServer()
	
	r := mux.NewRouter()
	
	// Game endpoints
	r.HandleFunc("/ws", gameServer.handleWebSocket)
	r.HandleFunc("/game-play/room-service", gameServer.handleRoomService)
	r.HandleFunc("/game-play/get-return-url", gameServer.handleReturnURL)
	
	// Static file serving
	r.PathPrefix("/").HandlerFunc(serveStaticFiles)
	
	port := "8080"
	fmt.Printf("🎮 Dots and Boxes Game Server starting on http://localhost:%s\n", port)
	fmt.Printf("📁 Serving files from: %s\n", getCurrentDir())
	fmt.Printf("🌐 Open http://localhost:%s in two browser tabs to test multiplayer\n", port)
	
	log.Fatal(http.ListenAndServe(":"+port, r))
}

func getCurrentDir() string {
	dir, err := filepath.Abs(".")
	if err != nil {
		return "current directory"
	}
	return dir
}
