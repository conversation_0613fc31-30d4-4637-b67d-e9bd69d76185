package main

import (
	"context"
	"errors"
	"log/slog"
	"sync"

	"github.com/BhaumikTalwar/Amrita/models/dto"
	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type PlayerState int

const (
	PlayerStateActive PlayerState = iota
	PlayerStateLeft
)

type PlayerInfo struct {
	Username     string
	SessionToken string
	Symbol       string
	State        PlayerState
}

type Choice string

const (
	ChoiceRock     Choice = "rock"
	ChoicePaper    Choice = "paper"
	ChoiceScissors Choice = "scissors"
	ChoiceNone     Choice = ""
)

type Round struct {
	Number        int
	Player1Choice Choice
	Player2Choice Choice
	Winner        string
}

type RockPaperScissors struct{}

func NewGame() gameapi.Game {
	return &RockPaperScissors{}
}

type RockPaperScissorsInstance struct {
	roomID string
	config gameapi.GameConfig
	mu     sync.Mutex

	playerInfo      map[string]*PlayerInfo
	currentRound    int
	rounds          []Round
	player1Wins     int
	player2Wins     int
	isGameOver      bool
	winner          string
	roundInProgress bool
}

func (g *RockPaperScissors) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		slog.Error("Error Cant initialize the Instance Config not valid", slog.Any("Error", err))
		return nil
	}
	return &RockPaperScissorsInstance{
		roomID:          roomID,
		config:          config,
		playerInfo:      make(map[string]*PlayerInfo),
		isGameOver:      false,
		rounds:          make([]Round, 0, 3),
		currentRound:    0,
		player1Wins:     0,
		player2Wins:     0,
		roundInProgress: false,
	}
}

func (g *RockPaperScissors) ValidateConfig(config gameapi.GameConfig) error {
	return nil
}

func (g *RockPaperScissorsInstance) HandlePlayerJoin(ctx context.Context, playerID string, sessionId string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.playerInfo) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	symbol := "Player1"
	if len(g.playerInfo) == 1 {
		symbol = "Player2"
	}

	g.playerInfo[playerID] = &PlayerInfo{
		Username:     name,
		SessionToken: sessionId,
		Symbol:       symbol,
		State:        PlayerStateActive,
	}

	if len(g.playerInfo) == 2 {
		g.startNewRound()
	}

	return nil
}

func (g *RockPaperScissorsInstance) startNewRound() {
	if g.isGameOver {
		return
	}

	g.currentRound++
	newRound := Round{
		Number:        g.currentRound,
		Player1Choice: ChoiceNone,
		Player2Choice: ChoiceNone,
		Winner:        "",
	}

	if len(g.rounds) < g.currentRound {
		g.rounds = append(g.rounds, newRound)
	} else {
		g.rounds[g.currentRound-1] = newRound
	}

	g.roundInProgress = true

}

func (g *RockPaperScissorsInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver || !g.roundInProgress {
		return errors.New("game is over or round not in progress")
	}

	info, exists := g.playerInfo[playerID]
	if !exists || info.State != PlayerStateActive {
		return errors.New("player not active in game")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	choiceStr, ok := move["choice"].(string)
	if !ok {
		return errors.New("invalid choice")
	}

	choice := Choice(choiceStr)
	if choice != ChoiceRock && choice != ChoicePaper && choice != ChoiceScissors {
		return errors.New("invalid choice value")
	}

	if g.currentRound > 0 && g.currentRound <= len(g.rounds) {
		if info.Symbol == "Player1" {
			g.rounds[g.currentRound-1].Player1Choice = choice
		} else {
			g.rounds[g.currentRound-1].Player2Choice = choice
		}

		round := &g.rounds[g.currentRound-1]
		if round.Player1Choice != ChoiceNone && round.Player2Choice != ChoiceNone {
			g.finishRound()
		}
	}

	return nil
}

func (g *RockPaperScissorsInstance) finishRound() {
	if !g.roundInProgress || g.currentRound == 0 {
		return
	}

	g.roundInProgress = false

	round := &g.rounds[g.currentRound-1]

	winner := g.determineRoundWinner(round.Player1Choice, round.Player2Choice)
	round.Winner = winner

	switch winner {
	case "Player1":
		g.player1Wins++
	case "Player2":
		g.player2Wins++
	}

	if g.player1Wins >= 2 {
		g.isGameOver = true
		g.winner = "Player1"
	} else if g.player2Wins >= 2 {
		g.isGameOver = true
		g.winner = "Player2"
	} else if g.currentRound >= 3 {
		g.isGameOver = true
		if g.player1Wins > g.player2Wins {
			g.winner = "Player1"
		} else if g.player2Wins > g.player1Wins {
			g.winner = "Player2"
		} else {
			g.winner = ""
		}
	}

	if !g.isGameOver {
		g.startNewRound()
	}
}

func (g *RockPaperScissorsInstance) determineRoundWinner(choice1, choice2 Choice) string {
	if choice1 == ChoiceNone && choice2 != ChoiceNone {
		return "Player2"
	}
	if choice2 == ChoiceNone && choice1 != ChoiceNone {
		return "Player1"
	}

	if choice1 == ChoiceNone || choice2 == ChoiceNone || choice1 == choice2 {
		return ""
	}

	if (choice1 == ChoiceRock && choice2 == ChoiceScissors) ||
		(choice1 == ChoiceScissors && choice2 == ChoicePaper) ||
		(choice1 == ChoicePaper && choice2 == ChoiceRock) {
		return "Player1"
	}

	return "Player2"
}

func (g *RockPaperScissorsInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	leaverInfo, exists := g.playerInfo[playerID]
	if !exists || leaverInfo.State == PlayerStateLeft {
		return nil
	}

	g.isGameOver = true
	leaverInfo.State = PlayerStateLeft

	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			g.winner = info.Symbol
			break
		}
	}

	return nil
}

func (g *RockPaperScissorsInstance) CalculateFinalResults(lobby_price float64, reason dto.GameEndReason, faultingPlayerID *string) []dto.PlayerResult {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.isGameOver && reason != dto.GameEndReasonInsufficientPlayers {
		return nil
	}

	results := make([]dto.PlayerResult, 0, len(g.playerInfo))

	for playerID, info := range g.playerInfo {
		result := dto.PlayerResult{
			UserID:       playerID,
			UserName:     info.Username,
			SessionToken: info.SessionToken,
			Status:       dto.PlayerStatusUnknown,
			Metadata:     map[string]interface{}{"symbol": info.Symbol},
		}

		if reason == dto.GameEndReasonInsufficientPlayers {
			result.Rank = 0
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Insufficient Players"}
		} else if info.State == PlayerStateLeft {
			result.Status = dto.PlayerStatusForfeit
			result.Rank = 2
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Forfeited game"}
		} else if g.winner != "" {
			if g.winner == info.Symbol {
				result.Status = dto.PlayerStatusWin
				result.Rank = 1
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 2 * lobby_price, Reason: "Winner"}
			} else {
				result.Status = dto.PlayerStatusLose
				result.Rank = 2
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Lost"}
			}
		} else if g.isGameOver {
			result.Status = dto.PlayerStatusDraw
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Draw"}
		}

		results = append(results, result)
	}

	return results
}

func (g *RockPaperScissorsInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	info, exists := g.playerInfo[playerID]
	if !exists {
		return g.getPublicStateInternal()
	}

	roundsCopy := make([]Round, len(g.rounds))
	copy(roundsCopy, g.rounds)

	if g.roundInProgress && g.currentRound > 0 && g.currentRound <= len(roundsCopy) {
		currentRound := &roundsCopy[g.currentRound-1]
		if info.Symbol == "Player1" {
			currentRound.Player2Choice = ChoiceNone
		} else {
			currentRound.Player1Choice = ChoiceNone
		}
	}

	return map[string]interface{}{
		"symbol":          info.Symbol,
		"currentRound":    g.currentRound,
		"rounds":          roundsCopy,
		"player1Wins":     g.player1Wins,
		"player2Wins":     g.player2Wins,
		"players":         g.buildPlayerNameMap(),
		"isGameOver":      g.isGameOver,
		"winner":          g.winner,
		"roundInProgress": g.roundInProgress,
	}
}

func (g *RockPaperScissorsInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.getPublicStateInternal()
}

func (g *RockPaperScissorsInstance) getPublicStateInternal() interface{} {
	roundsCopy := make([]Round, len(g.rounds))
	copy(roundsCopy, g.rounds)

	if g.roundInProgress && g.currentRound > 0 && g.currentRound <= len(roundsCopy) {
		currentRound := &roundsCopy[g.currentRound-1]
		currentRound.Player1Choice = ChoiceNone
		currentRound.Player2Choice = ChoiceNone
	}

	return map[string]interface{}{
		"currentRound":    g.currentRound,
		"rounds":          roundsCopy,
		"player1Wins":     g.player1Wins,
		"player2Wins":     g.player2Wins,
		"players":         g.buildPlayerNameMap(),
		"isGameOver":      g.isGameOver,
		"winner":          g.winner,
		"roundInProgress": g.roundInProgress,
	}
}

func (g *RockPaperScissorsInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *RockPaperScissorsInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *RockPaperScissorsInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.playerInfo = make(map[string]*PlayerInfo)
	g.currentRound = 0
	g.rounds = make([]Round, 0, 3)
	g.player1Wins = 0
	g.player2Wins = 0
	g.isGameOver = false
	g.winner = ""
	g.roundInProgress = false
	return nil
}

func (g *RockPaperScissorsInstance) buildPlayerNameMap() map[string]string {
	names := make(map[string]string)
	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			names[info.Symbol] = info.Username
		}
	}
	return names
}
