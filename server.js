const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Serve static files
app.use(express.static('.'));

// Game state
let gameRooms = new Map();
let playerCounter = 0;

class GameRoom {
    constructor() {
        this.players = {};
        this.currentTurn = '1';
        this.lines = {};
        this.boxes = {};
        this.scores = { '1': 0, '2': 0 };
        this.isGameOver = false;
        this.gridSize = 8;
        this.winner = null;
    }

    addPlayer(ws, playerId) {
        this.players[playerId] = { ws, symbol: playerId, name: `Player ${playerId}` };
        console.log(`Player ${playerId} joined the room`);
        
        // Send personal state
        ws.send(JSON.stringify({
            type: 'personal_state',
            state: {
                symbol: playerId,
                currentTurn: this.currentTurn,
                players: this.getPlayerNames(),
                lines: this.lines,
                boxes: this.boxes,
                scores: this.scores,
                isGameOver: this.isGameOver,
                gridSize: this.gridSize
            }
        }));

        // Broadcast player joined
        this.broadcast({
            type: 'player_joined',
            username: `Player ${playerId}`,
            state: this.getGameState()
        });

        // Start game if we have 2 players
        if (Object.keys(this.players).length === 2) {
            this.startGame();
        }
    }

    removePlayer(playerId) {
        if (this.players[playerId]) {
            delete this.players[playerId];
            console.log(`Player ${playerId} left the room`);
            
            // End game if a player leaves
            this.isGameOver = true;
            this.broadcast({
                type: 'game_over',
                winner: null,
                state: this.getGameState()
            });
        }
    }

    startGame() {
        console.log('Starting game with 2 players');
        this.broadcast({
            type: 'game_start',
            state: this.getGameState()
        });
    }

    getPlayerNames() {
        const names = {};
        Object.entries(this.players).forEach(([id, player]) => {
            names[id] = player.name;
        });
        return names;
    }

    getGameState() {
        return {
            currentTurn: this.currentTurn,
            players: this.getPlayerNames(),
            lines: this.lines,
            boxes: this.boxes,
            scores: this.scores,
            isGameOver: this.isGameOver,
            gridSize: this.gridSize,
            winner: this.winner
        };
    }

    makeMove(playerId, action) {
        if (this.isGameOver || this.currentTurn !== playerId) {
            return false;
        }

        const { startRow, startCol, endRow, endCol } = action;
        
        // Validate move
        if (!this.isValidMove(startRow, startCol, endRow, endCol)) {
            return false;
        }

        // Generate line ID
        const lineId = this.generateLineId(startRow, startCol, endRow, endCol);
        
        // Check if line already exists
        if (this.lines[lineId]) {
            return false;
        }

        // Add line
        this.lines[lineId] = playerId;
        console.log(`Player ${playerId} drew line: ${lineId}`);

        // Check for completed boxes
        const completedBoxes = this.checkCompletedBoxes(lineId);
        let boxesCompleted = false;

        completedBoxes.forEach(boxId => {
            if (!this.boxes[boxId]) {
                this.boxes[boxId] = playerId;
                this.scores[playerId]++;
                boxesCompleted = true;
                console.log(`Player ${playerId} completed box: ${boxId}`);
            }
        });

        // Switch turn only if no boxes were completed
        if (!boxesCompleted) {
            this.currentTurn = this.currentTurn === '1' ? '2' : '1';
        }

        // Check for game over
        this.checkGameOver();

        // Broadcast update
        this.broadcast({
            type: 'game_update',
            state: this.getGameState()
        });

        return true;
    }

    isValidMove(startRow, startCol, endRow, endCol) {
        // Check bounds
        if (startRow < 0 || startRow >= this.gridSize || startCol < 0 || startCol >= this.gridSize ||
            endRow < 0 || endRow >= this.gridSize || endCol < 0 || endCol >= this.gridSize) {
            return false;
        }

        // Check if adjacent
        const rowDiff = Math.abs(startRow - endRow);
        const colDiff = Math.abs(startCol - endCol);
        return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
    }

    generateLineId(startRow, startCol, endRow, endCol) {
        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minCol = Math.min(startCol, endCol);
        const maxCol = Math.max(startCol, endCol);

        if (minRow === maxRow) {
            // Horizontal line
            return `h-${minRow}-${minCol}`;
        } else {
            // Vertical line
            return `v-${minRow}-${minCol}`;
        }
    }

    checkCompletedBoxes(lineId) {
        const completedBoxes = [];
        const [type, row, col] = lineId.split('-').map((v, i) => i === 0 ? v : parseInt(v));

        if (type === 'h') {
            // Horizontal line - check boxes above and below
            if (row > 0) {
                const boxId = `box-${row - 1}-${col}`;
                if (this.isBoxComplete(row - 1, col)) {
                    completedBoxes.push(boxId);
                }
            }
            if (row < this.gridSize - 1) {
                const boxId = `box-${row}-${col}`;
                if (this.isBoxComplete(row, col)) {
                    completedBoxes.push(boxId);
                }
            }
        } else {
            // Vertical line - check boxes left and right
            if (col > 0) {
                const boxId = `box-${row}-${col - 1}`;
                if (this.isBoxComplete(row, col - 1)) {
                    completedBoxes.push(boxId);
                }
            }
            if (col < this.gridSize - 1) {
                const boxId = `box-${row}-${col}`;
                if (this.isBoxComplete(row, col)) {
                    completedBoxes.push(boxId);
                }
            }
        }

        return completedBoxes;
    }

    isBoxComplete(row, col) {
        const top = `h-${row}-${col}`;
        const bottom = `h-${row + 1}-${col}`;
        const left = `v-${row}-${col}`;
        const right = `v-${row}-${col + 1}`;

        return this.lines[top] && this.lines[bottom] && this.lines[left] && this.lines[right];
    }

    checkGameOver() {
        const totalPossibleBoxes = (this.gridSize - 1) * (this.gridSize - 1);
        const completedBoxes = Object.keys(this.boxes).length;

        if (completedBoxes === totalPossibleBoxes) {
            this.isGameOver = true;
            const score1 = this.scores['1'];
            const score2 = this.scores['2'];
            
            if (score1 > score2) {
                this.winner = '1';
            } else if (score2 > score1) {
                this.winner = '2';
            } else {
                this.winner = null; // Draw
            }

            console.log(`Game over! Winner: ${this.winner || 'Draw'}`);
        }
    }

    broadcast(message) {
        Object.values(this.players).forEach(player => {
            if (player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(JSON.stringify(message));
            }
        });
    }
}

// Routes
app.get('/game-play/room-service', (req, res) => {
    const wsUrl = `ws://localhost:3000`;
    res.json({ wsURL: wsUrl });
});

app.get('/game-play/get-return-url', (req, res) => {
    res.json({ returnUrl: '/' });
});

// WebSocket handling
wss.on('connection', (ws) => {
    console.log('New WebSocket connection');
    
    // Assign player to room (simple: one room for now)
    let room = gameRooms.get('room1');
    if (!room) {
        room = new GameRoom();
        gameRooms.set('room1', room);
    }

    // Assign player ID
    const playerId = Object.keys(room.players).length < 2 ? 
        (Object.keys(room.players).includes('1') ? '2' : '1') : null;

    if (!playerId) {
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Room is full'
        }));
        ws.close();
        return;
    }

    room.addPlayer(ws, playerId);

    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            console.log('Received message:', message);

            switch (message.type) {
                case 'game_action':
                    room.makeMove(playerId, message.action);
                    break;
                case 'ping':
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;
            }
        } catch (error) {
            console.error('Error processing message:', error);
        }
    });

    ws.on('close', () => {
        console.log('WebSocket connection closed');
        room.removePlayer(playerId);
    });
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Open two browser tabs to http://localhost:3000 to test the game');
});
