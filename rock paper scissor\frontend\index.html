<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GamyDay Games - Rock Paper Scissors</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
            background-color: #0a0a1a;
        }

        .font-fredoka {
            font-family: 'Fredoka', sans-serif;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        .text-glow {
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        @keyframes modalPop {
            from {
                transform: scale(0.7);
                opacity: 0;
            }

            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.6;
            }
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
            background: radial-gradient(ellipse at center, #101827, #0a0a1a);
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
        }

        .text-glow-cyan {
            text-shadow: 0 0 8px rgba(56, 189, 248, 0.7);
        }

        .text-glow-orange {
            text-shadow: 0 0 8px rgba(249, 115, 22, 0.7);
        }

        .choice-button {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .choice-button:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(56, 189, 248, 0.5);
        }

        .choice-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .choice-button.selected {
            background: rgba(56, 189, 248, 0.3);
            border-color: rgba(56, 189, 248, 0.8);
            transform: scale(1.05);
        }

        #modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 26, 0.8);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        #modal.show {
            display: flex;
        }

        .modal-content {
            background: linear-gradient(145deg, #1e1b4b, #171336);
            padding: 2.5rem;
            border-radius: 1rem;
            text-align: center;
            border: 1px solid rgba(56, 189, 248, 0.3);
            box-shadow: 0 0 30px rgba(56, 189, 248, 0.2);
            transform: scale(0.7);
            animation: modalPop 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards;
        }

        #countdownTimer {
            animation: pulse 1s infinite;
            display: inline-block;
        }

        #roundResults {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .round-result {
            padding: 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: fadeInUp 0.5s ease-out forwards;
        }

        .choice-display {
            font-size: 3rem;
            margin: 0 1rem;
        }
    </style>
</head>

<body class="bg-[#0a0a1a]">
    <audio id="gameStartSound" src="audio.wav" preload="auto"></audio>
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
            <div id="waitTimerContainer" class="text-center mt-2 text-gray-300 h-10"></div>

        </div>
        <div class="absolute bottom-16 w-full text-center text-lg text-yellow-300 font-semibold animate-pulse">
            <p>Warning: ⚠️ Don't close tab — amount may be deducted.</p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">GamyDay-Amrita</span>
        </div>
    </div>

    <div id="gameUI" class="flex flex-col items-center justify-center min-h-screen text-white p-4">
        <div class="w-full max-w-4xl mx-auto text-center mb-6">
            <h1 class="font-fredoka text-4xl md:text-5xl font-bold mb-2 text-white"
                style="text-shadow: 0 0 15px rgba(255,255,255,0.3);">Rock Paper Scissors</h1>
            <p class="text-indigo-300">Best of 3 rounds - First to win 2 rounds wins!</p>
        </div>

        <div class="flex justify-between w-full max-w-lg mb-4 text-lg">
            <div id="player1" class="font-semibold p-3 rounded-lg bg-black/20 border border-blue-400/30 shadow-lg">
                <span class="text-blue-400 text-glow-cyan">Player 1:</span> <span id="player1Name">Waiting...</span>
                <div class="text-sm text-gray-300 mt-1">Wins: <span id="player1Wins">0</span></div>
            </div>
            <div id="player2" class="font-semibold p-3 rounded-lg bg-black/20 border border-orange-400/30 shadow-lg">
                <span class="text-orange-400 text-glow-orange">Player 2:</span> <span id="player2Name">Waiting...</span>
                <div class="text-sm text-gray-300 mt-1">Wins: <span id="player2Wins">0</span></div>
            </div>
        </div>

        <div id="roundInfo" class="text-center mb-4">
            <div id="roundIndicator" class="text-2xl font-bold mb-2">Waiting for players...</div>
            <div id="roundTimer" class="text-xl text-yellow-400 font-medium"></div>
            <p id="timerInfo" class="text-sm text-gray-400 mt-1"></p>
        </div>

        <div id="gameArea" class="w-full max-w-2xl mx-auto">
            <!-- Choice buttons -->
            <div id="choiceButtons" class="flex justify-center gap-6 mb-8">
                <button id="rockBtn"
                    class="choice-button bg-gray-700 hover:bg-gray-600 text-white font-bold py-4 px-6 rounded-lg border-2 border-gray-500 flex flex-col items-center min-w-[120px]">
                    <div class="text-4xl mb-2">✊</div>
                    <div class="text-lg">Rock</div>
                </button>
                <button id="paperBtn"
                    class="choice-button bg-gray-700 hover:bg-gray-600 text-white font-bold py-4 px-6 rounded-lg border-2 border-gray-500 flex flex-col items-center min-w-[120px]">
                    <div class="text-4xl mb-2">🤚</div>
                    <div class="text-lg">Paper</div>
                </button>
                <button id="scissorsBtn"
                    class="choice-button bg-gray-700 hover:bg-gray-600 text-white font-bold py-4 px-6 rounded-lg border-2 border-gray-500 flex flex-col items-center min-w-[120px]">
                    <div class="text-4xl mb-2">✌️</div>
                    <div class="text-lg">Scissors</div>
                </button>
            </div>

            <!-- Round results display -->
            <div id="roundResults" class="space-y-4">
                <!-- Round results will be populated here -->
            </div>


        </div>
    </div>

    <div id="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="font-fredoka text-4xl font-bold text-white mb-4"></h2>
            <p id="modalMessage" class="text-xl text-gray-300 mb-8"></p>
            <div class="mt-4 text-lg text-yellow-300 font-semibold">
                <p>Warning: ⚠️ Don't refresh tab — amount may be deducted.</p>
            </div>
        </div>
    </div>



    <script>
        const DEBUG = true;
        const logger = {
            log: (...args) => DEBUG && console.log(...args),
            warn: (...args) => DEBUG && console.warn(...args),
            error: (...args) => DEBUG && console.error(...args),
        };

        const progressBar = document.getElementById('progressBar');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingScreen = document.getElementById('loadingScreen');
        const gameUI = document.getElementById('gameUI');
        const player1Name = document.getElementById('player1Name');
        const player2Name = document.getElementById('player2Name');
        const player1Wins = document.getElementById('player1Wins');
        const player2Wins = document.getElementById('player2Wins');
        const roundIndicator = document.getElementById('roundIndicator');
        const roundTimer = document.getElementById('roundTimer');
        const timerInfo = document.getElementById('timerInfo');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const choiceButtons = document.getElementById('choiceButtons');
        const rockBtn = document.getElementById('rockBtn');
        const paperBtn = document.getElementById('paperBtn');
        const scissorsBtn = document.getElementById('scissorsBtn');
        const roundResults = document.getElementById('roundResults');
        const currentChoices = document.getElementById('currentChoices');
        const player1Choice = document.getElementById('player1Choice');
        const player2Choice = document.getElementById('player2Choice');

        const messages = [
            "Warming up the engines...",
            "Connecting to the game servers...",
            "Looking for a game room...",
            "Loading awesome assets...",
            "Waiting for players...",
            "Assembling pixels...",
            "Almost there, get ready!",
            "Game ready!"
        ];

        let progress = 0;
        let messageIndex = 0;
        let ws;
        let mySymbol = null;
        let players = null;
        let gameStarted = false;
        let gameOver = false;
        let gameState = {};
        let loadingIntervals = {};
        let waitTimerInterval = null;
        let roundTimerInterval = null;
        let clientInitiatedClose = false;
        let selectedChoice = null;
        let currentRound = 0;

        const choiceEmojis = {
            'rock': '✊',
            'paper': '🤚',
            'scissors': '✌️',
            '': '?'
        };

        window.onload = function () {
            const cleanUrl = window.location.origin + '/game-play/';
            window.history.replaceState({}, document.title, cleanUrl);
            initializeChoiceButtons();
            fetchWebSocketUrl();
            simulateLoading();
        };

        function initializeChoiceButtons() {
            rockBtn.addEventListener('click', () => makeChoice('rock'));
            paperBtn.addEventListener('click', () => makeChoice('paper'));
            scissorsBtn.addEventListener('click', () => makeChoice('scissors'));

            disableChoiceButtons();
        }

        function makeChoice(choice) {
            if (selectedChoice || gameOver || !gameState.roundInProgress) {
                return;
            }

            selectedChoice = choice;

            if (roundTimerInterval) {
                clearInterval(roundTimerInterval);
                roundTimerInterval = null;
                roundTimer.textContent = 'Choice locked in!';
                timerInfo.textContent = 'Waiting for opponent...';
            }

            disableChoiceButtons()

            document.getElementById(choice + 'Btn').classList.add('selected');

            if (ws && ws.readyState === WebSocket.OPEN) {
                const action = {type: 'game_action', action: {choice: choice}};
                logger.log('Sending choice:', action);
                ws.send(JSON.stringify(action));
            }
        }

        function enableChoiceButtons() {
            selectedChoice = null;
            document.querySelectorAll('.choice-button').forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('selected');
            });
        }

        function disableChoiceButtons() {
            document.querySelectorAll('.choice-button').forEach(btn => {
                btn.disabled = true;
                btn.classList.remove('selected');
            });
        }

        function simulateLoading() {
            const messageInterval = setInterval(() => {
                if (messageIndex < messages.length - 1) {
                    messageIndex++;
                    loadingMessage.innerText = messages[messageIndex];
                    if (messageIndex === 4) {
                        startWaitingTimer();
                    }
                }
            }, 1200);

            const progressInterval = setInterval(() => {
                if (progress < 80) {
                    progress += Math.random() * 2.5;
                    progressBar.style.width = Math.min(progress, 80) + '%';
                }
            }, 100);

            loadingIntervals = {messageInterval, progressInterval};
        }

        function startWaitingTimer() {
            const waitTimerContainer = document.getElementById('waitTimerContainer');
            if (!waitTimerContainer) return;

            waitTimerContainer.innerHTML = `
                <p class="text-sm">Estimated wait time: 5 minutes.</p>
                <p id="waitTimerDisplay" class="text-lg text-amber-300 font-semibold"></p>
            `;
            const timerDisplay = document.getElementById('waitTimerDisplay');

            let seconds = 300;

            const updateTimer = () => {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                if (timerDisplay) {
                    timerDisplay.innerText = `Time remaining: ${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
                }
            };

            updateTimer();

            waitTimerInterval = setInterval(() => {
                seconds--;
                updateTimer();

                if (seconds <= 0) {
                    clearInterval(waitTimerInterval);
                    const timerDisplay = document.getElementById('waitTimerDisplay');
                    if (timerDisplay) {
                        timerDisplay.innerText = 'Time is up! Redirecting Safely...';
                    }
                }
            }, 1000);
        }

        function stopWaitingTimer() {
            if (waitTimerInterval) {
                clearInterval(waitTimerInterval);
                const waitTimerContainer = document.getElementById('waitTimerContainer');
                if (waitTimerContainer) {
                    waitTimerContainer.innerHTML = '';
                }
            }
        }

        async function fetchWebSocketUrl() {
            try {
                const response = await fetch('/game-play/room-service', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                if (data.wsURL) {
                    connectWebSocket(data.wsURL);
                } else {
                    throw new Error('No WebSocket URL provided');
                }
            } catch (error) {
                logger.error('Error fetching WebSocket URL:', error);
                loadingMessage.innerText = 'Error connecting to server...';
            }
        }

        function connectWebSocket(url) {
            logger.log('Connecting to WebSocket:', url);
            ws = new WebSocket(url);

            ws.onopen = function (event) {
                logger.log('WebSocket connected');
                startHeartbeat();
            };

            ws.onmessage = function (event) {
                const data = JSON.parse(event.data);
                logger.log('Received message:', data);
                handleWebSocketMessage(data);
            };

            ws.onclose = function (event) {
                logger.log('WebSocket closed:', event);
                if (!clientInitiatedClose && !gameOver) {
                    showError('Connection lost');
                }
            };

            ws.onerror = function (error) {
                logger.error('WebSocket error:', error);
                showError('Connection error');
            };
        }

        function startHeartbeat() {
            setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({type: 'ping'}));
                }
            }, 30000);
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'game_state':
                    handleGameState(data.state);
                    break;
                case 'game_start':
                    handleGameStart(data.state);
                    break;
                case 'game_update':
                    handleGameUpdate(data.state);
                    break;
                case 'personal_state':
                    handlePersonalState(data.state);
                    break;
                case 'player_joined':
                    handlePlayerJoined(data.state);
                    break;
                case 'player_left':
                    handlePlayerLeft(data.state);
                    break;
                case 'game_over':
                    handleGameOver(data);
                    break;
                case 'pong':
                    break;
                case 'error':
                    showError(data.message || 'Game error occurred');
                    break;
                default:
                    logger.warn('Unknown message type:', data.type);
            }
        }

        function handleGameState(state) {
            logger.log('Game state:', state);
            gameState = state;

            if (state.players) {
                players = state.players;
                updatePlayerDisplay();
            }

            currentRound = state.currentRound;
            updateGameDisplay();
        }

        function handleGameStart(data) {
            logger.log('Game started:', data);
            gameStarted = true;
            gameState = data;

            if (data.players) {
                players = data.players;
            }

            currentRound = data.currentRound;

            finishLoading();
            updatePlayerDisplay();
            updateGameDisplay();
        }

        function handleGameUpdate(data) {
            logger.log('Game update:', data);
            const prevRound = gameState.currentRound;

            gameState = data;

            updatePlayerDisplay();
            updateGameDisplay();
            updateRoundDisplay();

            if (!gameState.isGameOver && currentRound < gameState.currentRound) {
                enableChoiceButtons();
                selectedChoice = null;
            }

            currentRound = gameState.currentRound;
        }

        function handlePersonalState(data) {
            logger.log('Personal state:', data);
            mySymbol = data.symbol;
            gameState = {...gameState, ...data};
            updateGameDisplay();
        }

        function handlePlayerJoined(data) {
            logger.log('Player joined:', data);
            if (data.players) {
                players = data.players;
                updatePlayerDisplay();
            }
        }

        function handlePlayerLeft(data) {
            logger.log('Player left:', data);
            if (data.players) {
                players = data.players;
                updatePlayerDisplay();
            }
        }

        function handleGameOver(data) {
            logger.log('Game over:', data);
            gameOver = true;
            gameState = data.state;

            if (roundTimerInterval) {
                clearInterval(roundTimerInterval);
            }

            disableChoiceButtons();
            updateGameDisplay();

            setTimeout(() => {
                showGameOverModal(data);
            }, 1000);
        }

        function updatePlayerDisplay() {
            if (players['Player1']) {
                player1Name.textContent = players['Player1'];
            }
            if (players['Player2']) {
                player2Name.textContent = players['Player2'];
            }

            if (gameState.player1Wins !== undefined) {
                player1Wins.textContent = gameState.player1Wins;
            }
            if (gameState.player2Wins !== undefined) {
                player2Wins.textContent = gameState.player2Wins;
            }
        }

        function updateGameDisplay() {
            if (!gameState) return;

            if (gameState.currentRound > 0) {
                roundIndicator.textContent = `Round ${gameState.currentRound}/3`;
            } else {
                roundIndicator.textContent = gameStarted ? 'Starting...' : 'Waiting for players...';
            }

            if (gameState.roundInProgress && !selectedChoice) {
                if (!roundTimerInterval) {
                    let seconds = 30;
                    roundTimer.textContent = `Time left: ${seconds}s`;
                    timerInfo.textContent = 'If no choice is made, one will be selected for you.';

                    roundTimerInterval = setInterval(() => {
                        seconds--;
                        roundTimer.textContent = `Time left: ${seconds}s`;
                        if (seconds <= 0) {
                            clearInterval(roundTimerInterval);
                            roundTimerInterval = null;
                            roundTimer.textContent = "Time's up!";
                            timerInfo.textContent = 'Making a random choice...';
                            makeRandomChoice();
                        }
                    }, 1000);
                }
            } else {
                if (roundTimerInterval) {
                    clearInterval(roundTimerInterval);
                    roundTimerInterval = null;
                }
                roundTimer.textContent = '';
                timerInfo.textContent = '';
            }

            if (gameState.roundInProgress && !selectedChoice && !gameOver) {
                enableChoiceButtons();
            } else {
                disableChoiceButtons();
            }

            updateRoundDisplay();
        }

        function makeRandomChoice() {
            if (selectedChoice) return;
            const choices = ['rock', 'paper', 'scissors'];
            const randomChoice = choices[Math.floor(Math.random() * choices.length)];
            makeChoice(randomChoice);
        }



        function startNextRoundCountdown() {
            let countdown = 3;
            roundTimer.innerHTML = `<span id="countdownTimer">Next round in ${countdown}s...</span>`;
            roundTimerInterval = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    roundTimer.innerHTML = `<span id="countdownTimer">Next round in ${countdown}s...</span>`;
                } else {
                    clearInterval(roundTimerInterval);
                    roundTimerInterval = null;
                    roundTimer.textContent = '';
                }
            }, 1000);
        }



        function updateRoundDisplay() {
            if (!gameState.rounds) {
                roundResults.innerHTML = '';
                return;
            }

            const roundsToDisplay = [...gameState.rounds].reverse();
            roundResults.innerHTML = '';

            roundsToDisplay.forEach(round => {
                if (round.Number > 0 && (round.Player1Choice || round.Player2Choice)) {
                    const player1Choice = round.Player1Choice || '';
                    const player2Choice = round.Player2Choice || '';
                    const winner = round.Winner;

                    let resultText = '';
                    let resultClass = 'text-gray-400';
                    if (winner === 'Player1') {
                        resultText = `${players['Player1'] || 'Player 1'} wins!`;
                        resultClass = 'text-blue-400';
                    } else if (winner === 'Player2') {
                        resultText = `${players['Player2'] || 'Player 2'} wins!`;
                        resultClass = 'text-orange-400';
                    } else if (winner === '') {
                        resultText = 'Draw!';
                    } else {
                        resultText = 'Round in Progress...';
                    }

                    const resultCard = `
                        <div class="round-result bg-black/30 border border-gray-600 rounded-lg p-4">
                            <div class="text-xl font-bold mb-3 text-white">Round ${round.Number}</div>
                            <div class="flex justify-around items-center mb-3">
                                <div class="text-center">
                                    <div class="text-sm text-gray-300">${players['Player1'] || 'Player 1'}</div>
                                    <div class="text-5xl mt-1">${choiceEmojis[player1Choice] || "?"}</div>
                                </div>
                                <div class="text-4xl text-gray-500 font-bold">VS</div>
                                <div class="text-center">
                                    <div class="text-sm text-gray-300">${players['Player2'] || 'Player 2'}</div>
                                    <div class="text-5xl mt-1">${choiceEmojis[player2Choice] || "?"}</div>
                                </div>
                            </div>
                            <div class="text-center text-2xl font-semibold ${resultClass}">${resultText}</div>
                        </div>
                    `;
                    roundResults.innerHTML += resultCard;
                }
            });

            if (gameState.currentRound > 0) {
                roundIndicator.textContent = `Round ${gameState.currentRound}/3`;
            }
        }

        function finishLoading() {
            Object.values(loadingIntervals).forEach(interval => clearInterval(interval));
            stopWaitingTimer();

            progress = 100;
            progressBar.style.width = '100%';
            loadingMessage.innerText = 'Game ready!';

            setTimeout(() => {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    gameUI.classList.add('active');
                }, 500);
            }, 1000);
        }

        function showError(message) {
            modalTitle.textContent = 'Connection Error';
            modalMessage.textContent = message;
            modal.classList.add('show');

            setTimeout(() => {
                redirectToReturn();
            }, 3000);
        }

        function showGameOverModal(data) {
            let title = 'Game Over';
            let message = "Game has Ended";
            let reason = data.reason;


            if (reason === 'COMPLETED') {
                if (data.winner) {
                    const winnerName = players[data.winner] || data.winner;
                    if (data.winner === mySymbol) {
                        title = '🎉 You Won!';
                        message = `Congratulations! You defeated your opponent in Rock Paper Scissors!`;
                        triggerConfetti();
                    } else {
                        title = '😔 You Lost';
                        message = `${winnerName} won this round. Better luck next time!`;
                    }
                } else {
                    title = '🤝 It\'s a Draw!';
                    message = 'The game ended in a tie. Great match!';
                }
            } else {
                title = data.message;
                message = data.detailedMessage;
            }

            modalTitle.textContent = title;
            modalMessage.textContent = message;
            modal.classList.add('show');

            setTimeout(() => {
                redirectToReturn();
            }, 5000);
        }

        function triggerConfetti() {
            const duration = 3000;
            const animationEnd = Date.now() + duration;
            const defaults = {startVelocity: 30, spread: 360, ticks: 60, zIndex: 3000};

            function randomInRange(min, max) {
                return Math.random() * (max - min) + min;
            }

            const interval = setInterval(function () {
                const timeLeft = animationEnd - Date.now();

                if (timeLeft <= 0) {
                    return clearInterval(interval);
                }

                const particleCount = 50 * (timeLeft / duration);

                confetti(Object.assign({}, defaults, {
                    particleCount,
                    origin: {x: randomInRange(0.1, 0.3), y: Math.random() - 0.2}
                }));
                confetti(Object.assign({}, defaults, {
                    particleCount,
                    origin: {x: randomInRange(0.7, 0.9), y: Math.random() - 0.2}
                }));
            }, 250);
        }

        function redirectToReturn() {
            clientInitiatedClose = true;

            fetch('/game-play/get-return-url', {
                credentials: 'include'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.returnUrl) {
                        window.location.href = data.returnUrl;
                    } else {
                        window.location.href = 'https://gamyday.com';
                    }
                })
                .catch(error => {
                    logger.error('Error getting return URL:', error);
                    window.location.href = 'https://gamyday.com';
                });
        }

        window.addEventListener('beforeunload', function (e) {
            if (!gameOver && !clientInitiatedClose) {
                e.preventDefault();
                e.returnValue = '';
                return '';
            }
        });

        document.addEventListener('visibilitychange', function () {
            if (document.hidden) {
                logger.log('Page hidden');
            } else {
                logger.log('Page visible');
            }
        });
    </script>
</body>

</html>
